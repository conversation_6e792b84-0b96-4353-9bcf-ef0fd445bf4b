#!/usr/bin/env python3
"""
Test script for configuration system
"""

import json
import os
from pathlib import Path

def test_config_system():
    """Test the configuration system"""
    print("🧪 Testing Configuration System")
    print("=" * 40)
    
    try:
        # Test 1: Import config module
        print("1. Testing config module import...")
        from config import get_config, get_config_manager, AgentConfig
        print("   ✅ Config module imported successfully")
        
        # Test 2: Create config manager
        print("2. Testing config manager creation...")
        config_manager = get_config_manager()
        print("   ✅ Config manager created")
        
        # Test 3: Get configuration
        print("3. Testing configuration loading...")
        config = get_config()
        print(f"   ✅ Configuration loaded")
        print(f"   Model: {config.model_name}")
        print(f"   Max Results: {config.max_search_results}")
        print(f"   Debug Mode: {config.debug_mode}")
        
        # Test 4: Display config status
        print("4. Testing config status display...")
        config_manager.print_config_status()
        print("   ✅ Config status displayed")
        
        # Test 5: Check if agent can be initialized
        print("5. Testing agent initialization...")
        try:
            from Agent_WebSearch import EnhancedResearchAgent
            # This will fail if OpenAI key is not set, which is expected
            agent = EnhancedResearchAgent()
            print("   ✅ Agent initialized successfully")
        except ValueError as e:
            if "OpenAI API key" in str(e):
                print("   ⚠️ Agent initialization failed (expected - no API key)")
                print(f"   Error: {e}")
            else:
                print(f"   ❌ Unexpected error: {e}")
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
        
        print("\n" + "=" * 40)
        print("🎉 Configuration system test completed!")
        
        # Show next steps
        print("\n📝 Next Steps:")
        if not config.openai_api_key or config.openai_api_key == "your_openai_api_key_here":
            print("1. Run: python setup_config.py")
            print("2. Add your OpenAI API key")
            print("3. Run: python Agent_WebSearch.py")
        else:
            print("1. Run: python Agent_WebSearch.py")
            print("2. Your configuration looks good!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_config_system()
