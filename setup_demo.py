#!/usr/bin/env python3
"""
Setup script for Enhanced Research Agent Demo
Makes it easy to get started with the demo
"""

import os
import subprocess
import sys
from pathlib import Path

def check_python_version():
    """Ensure Python 3.8+ is being used"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required packages"""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def setup_environment():
    """Set up environment file if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("\n🔧 Setting up environment file...")
        env_file.write_text(env_example.read_text())
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your API keys")
        return True
    elif env_file.exists():
        print("✅ Environment file already exists")
        return True
    else:
        print("⚠️ No environment template found")
        return False

def check_api_keys():
    """Check if API keys are configured"""
    config_file = Path("agent_config.json")
    env_file = Path(".env")

    has_openai = False
    has_tavily = False

    # Check JSON config file first
    if config_file.exists():
        try:
            import json
            with open(config_file, 'r') as f:
                config = json.load(f)
            has_openai = config.get("openai_api_key") and config.get("openai_api_key") != "your_openai_api_key_here"
            has_tavily = config.get("tavily_api_key") and config.get("tavily_api_key") != "your_tavily_api_key_here"
        except:
            pass

    # Check .env file as fallback
    if not has_openai and env_file.exists():
        content = env_file.read_text()
        has_openai = "OPENAI_API_KEY=" in content and "your_openai_api_key_here" not in content
        if not has_tavily:
            has_tavily = "TAVILY_API_KEY=" in content and "your_tavily_api_key_here" not in content

    print(f"\n🔑 API Key Status:")
    print(f"  OpenAI: {'✅ Configured' if has_openai else '⚠️ Not configured'}")
    print(f"  Tavily: {'✅ Configured' if has_tavily else '⚠️ Not configured (optional)'}")

    if not has_openai:
        print("\n📝 To get full functionality, configure your API keys:")
        print("   Option 1: Run 'python setup_config.py' (recommended)")
        print("   Option 2: Edit agent_config.json manually")
        print("   Option 3: Set environment variables")
        print("   Get OpenAI key at: https://platform.openai.com/api-keys")

    if not has_tavily:
        print("📝 For real web search, add Tavily API key")
        print("   Get one at: https://tavily.com (optional - demo works without it)")

    return has_openai

def run_demo():
    """Run the demo"""
    print("\n🚀 Starting Enhanced Research Agent Demo...")
    try:
        subprocess.run([sys.executable, "Agent_WebSearch.py"])
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"❌ Error running demo: {e}")

def main():
    """Main setup function"""
    print("🤖 Enhanced Research Agent - Demo Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Setup environment
    setup_environment()
    
    # Check API keys
    has_keys = check_api_keys()
    
    print("\n" + "=" * 50)
    print("🎉 Setup Complete!")
    
    if has_keys:
        print("✅ Ready for full demo with real AI capabilities")
    else:
        print("⚠️ Demo will run with mock data (still impressive!)")
        print("   Add API keys to .env for full functionality")
    
    # Ask if user wants to run demo now
    if not has_keys:
        print("\n💡 Would you like to configure your API keys now?")
        response = input("Run configuration setup? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            try:
                subprocess.run([sys.executable, "setup_config.py"])
                print("\n🔄 Checking configuration again...")
                has_keys = check_api_keys()
            except Exception as e:
                print(f"❌ Failed to run setup: {e}")

    response = input("\n🚀 Run demo now? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        run_demo()
    else:
        print("\n📝 To run demo later:")
        print("   python Agent_WebSearch.py")
        print("   python setup_config.py  (to configure API keys)")
        print("📖 See README.md for more information")

if __name__ == "__main__":
    main()
