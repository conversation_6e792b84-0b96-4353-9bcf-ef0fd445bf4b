"""
Configuration management for Enhanced Research Agent
Handles API keys and settings from multiple sources
"""

import os
import json
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

@dataclass
class AgentConfig:
    """Configuration class for the Research Agent"""
    # API Keys
    openai_api_key: Optional[str] = None
    tavily_api_key: Optional[str] = None
    serp_api_key: Optional[str] = None
    google_cse_id: Optional[str] = None
    google_api_key: Optional[str] = None
    
    # Agent Settings
    max_search_results: int = 5
    max_content_length: int = 8000
    research_timeout: int = 30
    model_name: str = "gpt-4"
    temperature: float = 0.3
    
    # Debug and Logging
    debug_mode: bool = False
    log_level: str = "INFO"

class ConfigManager:
    """Manages configuration from multiple sources with priority order"""
    
    def __init__(self, config_file: str = "agent_config.json"):
        self.config_file = Path(config_file)
        self.config = AgentConfig()
        self._load_configuration()
    
    def _load_configuration(self):
        """Load configuration from multiple sources in priority order:
        1. JSON config file (highest priority)
        2. Environment variables
        3. .env file
        4. Default values (lowest priority)
        """
        # Step 1: Load from .env file first
        load_dotenv()
        
        # Step 2: Load from environment variables
        self._load_from_env()
        
        # Step 3: Load from JSON config file (overrides env vars)
        self._load_from_json()
        
        # Step 4: Validate configuration
        self._validate_config()
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # API Keys
        self.config.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.config.tavily_api_key = os.getenv("TAVILY_API_KEY")
        self.config.serp_api_key = os.getenv("SERP_API_KEY")
        self.config.google_cse_id = os.getenv("GOOGLE_CSE_ID")
        self.config.google_api_key = os.getenv("GOOGLE_API_KEY")
        
        # Agent Settings
        self.config.max_search_results = int(os.getenv("MAX_SEARCH_RESULTS", "5"))
        self.config.max_content_length = int(os.getenv("MAX_CONTENT_LENGTH", "8000"))
        self.config.research_timeout = int(os.getenv("RESEARCH_TIMEOUT", "30"))
        self.config.model_name = os.getenv("MODEL_NAME", "gpt-4")
        self.config.temperature = float(os.getenv("TEMPERATURE", "0.3"))
        
        # Debug Settings
        self.config.debug_mode = os.getenv("DEBUG_MODE", "false").lower() == "true"
        self.config.log_level = os.getenv("LOG_LEVEL", "INFO")
    
    def _load_from_json(self):
        """Load configuration from JSON file"""
        if not self.config_file.exists():
            # Create default config file
            self._create_default_config_file()
            return
        
        try:
            with open(self.config_file, 'r') as f:
                json_config = json.load(f)
            
            # Update config with JSON values (only if they exist and are not None)
            for key, value in json_config.items():
                if hasattr(self.config, key) and value is not None:
                    setattr(self.config, key, value)
                    
        except (json.JSONDecodeError, FileNotFoundError) as e:
            print(f"Warning: Could not load config file {self.config_file}: {e}")
            print("Using environment variables and defaults")
    
    def _create_default_config_file(self):
        """Create a default configuration file"""
        default_config = {
            "openai_api_key": None,
            "tavily_api_key": None,
            "serp_api_key": None,
            "google_cse_id": None,
            "google_api_key": None,
            "max_search_results": 5,
            "max_content_length": 8000,
            "research_timeout": 30,
            "model_name": "gpt-4",
            "temperature": 0.3,
            "debug_mode": False,
            "log_level": "INFO"
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            print(f"Created default config file: {self.config_file}")
            print("You can edit this file to set your API keys and preferences")
        except Exception as e:
            print(f"Warning: Could not create config file: {e}")
    
    def _validate_config(self):
        """Validate the loaded configuration"""
        if not self.config.openai_api_key:
            print("⚠️  Warning: No OpenAI API key found!")
            print("   Set OPENAI_API_KEY in environment or agent_config.json")
        
        if not self.config.tavily_api_key:
            print("ℹ️  Info: No Tavily API key found - will use mock search data")
            print("   Set TAVILY_API_KEY for real web search capabilities")
    
    def get_config(self) -> AgentConfig:
        """Get the loaded configuration"""
        return self.config
    
    def update_config(self, **kwargs):
        """Update configuration values"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def save_config(self):
        """Save current configuration to JSON file"""
        config_dict = {
            key: getattr(self.config, key) 
            for key in self.config.__dataclass_fields__.keys()
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config_dict, f, indent=2)
            print(f"Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def print_config_status(self):
        """Print current configuration status"""
        print("\n🔧 Configuration Status:")
        print("-" * 30)
        
        # API Keys Status
        print("API Keys:")
        print(f"  OpenAI: {'✅ Set' if self.config.openai_api_key else '❌ Missing'}")
        print(f"  Tavily: {'✅ Set' if self.config.tavily_api_key else '⚠️ Missing (optional)'}")
        print(f"  SERP: {'✅ Set' if self.config.serp_api_key else '⚠️ Missing (optional)'}")
        
        # Settings
        print(f"\nSettings:")
        print(f"  Model: {self.config.model_name}")
        print(f"  Max Results: {self.config.max_search_results}")
        print(f"  Timeout: {self.config.research_timeout}s")
        print(f"  Debug Mode: {self.config.debug_mode}")

# Global config manager instance
_config_manager = None

def get_config() -> AgentConfig:
    """Get the global configuration instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager.get_config()

def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

# Convenience function for quick setup
def setup_config(config_file: str = "agent_config.json") -> AgentConfig:
    """Setup and return configuration"""
    global _config_manager
    _config_manager = ConfigManager(config_file)
    return _config_manager.get_config()
