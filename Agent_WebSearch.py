import openai
import requests
import json
import os
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from bs4 import BeautifulSoup
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.markdown import Markdown

# Import our configuration system
from config import get_config, get_config_manager, AgentConfig

# Initialize rich console for beautiful output
console = Console()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ResearchFinding:
    """Structured data for research findings"""
    title: str
    url: str
    content: str
    relevance_score: float
    source_type: str
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ResearchPlan:
    """Structured research plan"""
    strategy: str
    search_queries: List[str]
    expected_sources: List[str]
    focus_areas: List[str]

class EnhancedResearchAgent:
    """
    An intelligent research agent that can:
    1. Plan research strategies using AI
    2. Execute real web searches
    3. Extract and process content
    4. Synthesize comprehensive answers
    5. Maintain conversation memory
    """

    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the research agent with configuration

        Args:
            config_file: Path to JSON config file (optional)
        """
        # Step 1: Load configuration as the first step
        console.print("[bold blue]🔧 Loading configuration...[/bold blue]")

        if config_file:
            from config import setup_config
            self.config = setup_config(config_file)
        else:
            self.config = get_config()

        # Display configuration status
        config_manager = get_config_manager()
        config_manager.print_config_status()

        # Step 2: Initialize API clients with loaded config
        self._initialize_clients()

        # Step 3: Set up logging based on config
        self._setup_logging()

        # Memory and state
        self.memory: List[Dict[str, Any]] = []
        self.conversation_history: List[Dict[str, str]] = []

        console.print(Panel.fit(
            "[bold green]🤖 Enhanced Research Agent Initialized[/bold green]\n"
            f"Model: {self.config.model_name} | Max Results: {self.config.max_search_results}\n"
            "Ready to conduct intelligent research with real web search capabilities!",
            border_style="green"
        ))

    def _initialize_clients(self):
        """Initialize API clients based on configuration"""
        # Validate OpenAI API key
        if not self.config.openai_api_key or self.config.openai_api_key == "your_openai_api_key_here":
            console.print("[red]❌ OpenAI API key not configured![/red]")
            console.print("Please set your API key in agent_config.json or environment variables")
            raise ValueError("OpenAI API key is required")

        # Initialize OpenAI client
        try:
            self.openai_client = openai.OpenAI(api_key=self.config.openai_api_key)
            console.print("[green]✅ OpenAI client initialized[/green]")
        except Exception as e:
            console.print(f"[red]❌ Failed to initialize OpenAI client: {e}[/red]")
            raise

        # Set Tavily API key (optional)
        if self.config.tavily_api_key and self.config.tavily_api_key != "your_tavily_api_key_here":
            console.print("[green]✅ Tavily API key configured[/green]")
        else:
            console.print("[yellow]⚠️ Tavily API key not configured - will use mock search data[/yellow]")

    def _setup_logging(self):
        """Setup logging based on configuration"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        if self.config.debug_mode:
            console.print("[yellow]🐛 Debug mode enabled[/yellow]")
            logging.getLogger().setLevel(logging.DEBUG)

    async def research_and_answer(self, question: str) -> str:
        """
        Main research workflow that orchestrates the entire process
        """
        console.print(f"\n[bold blue]🤖 Research Agent received question:[/bold blue] {question}")

        # Add to conversation history
        self.conversation_history.append({"role": "user", "content": question})

        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:

                # Step 1: Intelligent Research Planning
                planning_task = progress.add_task("🧠 Creating intelligent research plan...", total=None)
                plan = await self.create_intelligent_research_plan(question)
                progress.update(planning_task, completed=True)

                self._display_research_plan(plan)

                # Step 2: Execute Real Web Research
                research_task = progress.add_task("🔍 Conducting web research...", total=None)
                findings = await self.execute_real_research(plan)
                progress.update(research_task, completed=True)

                console.print(f"[green]✅ Found {len(findings)} relevant sources[/green]")

                # Step 3: Intelligent Answer Synthesis
                synthesis_task = progress.add_task("🧩 Synthesizing comprehensive answer...", total=None)
                answer = await self.synthesize_intelligent_answer(question, findings, plan)
                progress.update(synthesis_task, completed=True)

            # Store in memory
            self._store_research_session(question, plan, findings, answer)

            # Add to conversation history
            self.conversation_history.append({"role": "assistant", "content": answer})

            console.print("\n[bold green]✅ Research Complete![/bold green]")
            return answer

        except Exception as e:
            logger.error(f"Research failed: {str(e)}")
            console.print(f"[red]❌ Research failed: {str(e)}[/red]")
            return f"I apologize, but I encountered an error during research: {str(e)}"
    
    async def create_intelligent_research_plan(self, question: str) -> ResearchPlan:
        """
        Use OpenAI to create an intelligent, comprehensive research plan
        """
        try:
            planning_prompt = f"""
            You are an expert research strategist. Given the following question, create a comprehensive research plan.

            Question: {question}

            Please provide:
            1. Overall research strategy
            2. 3-5 specific search queries that would yield the best results
            3. Types of sources to prioritize
            4. Key focus areas to investigate

            Consider:
            - Current date context (it's {datetime.now().strftime('%Y-%m-%d')})
            - Whether this requires recent/current information
            - Technical depth needed
            - Practical vs theoretical focus

            Respond in JSON format:
            {{
                "strategy": "Brief description of overall approach",
                "search_queries": ["query1", "query2", "query3"],
                "expected_sources": ["source_type1", "source_type2"],
                "focus_areas": ["area1", "area2", "area3"]
            }}
            """

            response = self.openai_client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": planning_prompt}],
                temperature=self.config.temperature
            )

            plan_data = json.loads(response.choices[0].message.content)
            return ResearchPlan(**plan_data)

        except Exception as e:
            logger.warning(f"AI planning failed, using fallback: {str(e)}")
            return self._create_fallback_plan(question)

    def _create_fallback_plan(self, question: str) -> ResearchPlan:
        """Fallback research plan if AI planning fails"""
        if "latest" in question.lower() or "recent" in question.lower() or "2024" in question:
            return ResearchPlan(
                strategy="Search for recent information with current context",
                search_queries=[question, f"{question} 2024", f"latest {question}"],
                expected_sources=["news", "blogs", "documentation"],
                focus_areas=["current trends", "recent developments", "best practices"]
            )
        elif "how to" in question.lower():
            return ResearchPlan(
                strategy="Find step-by-step guides and practical examples",
                search_queries=[question, f"{question} tutorial", f"{question} guide"],
                expected_sources=["tutorials", "documentation", "forums"],
                focus_areas=["step-by-step process", "examples", "common pitfalls"]
            )
        else:
            return ResearchPlan(
                strategy="Comprehensive information gathering",
                search_queries=[question, f"{question} overview", f"{question} examples"],
                expected_sources=["documentation", "articles", "forums"],
                focus_areas=["fundamentals", "examples", "best practices"]
            )
    
    async def execute_real_research(self, plan: ResearchPlan) -> List[ResearchFinding]:
        """
        Execute real web research using Tavily API
        """
        all_findings = []

        try:
            if not self.config.tavily_api_key or self.config.tavily_api_key == "your_tavily_api_key_here":
                console.print("[yellow]⚠️ No Tavily API key found, using mock data[/yellow]")
                return self._get_mock_findings()

            # Import tavily here to avoid import errors if not installed
            from tavily import TavilyClient
            tavily_client = TavilyClient(api_key=self.config.tavily_api_key)

            for query in plan.search_queries:
                console.print(f"  🔍 Searching: [italic]{query}[/italic]")

                try:
                    # Perform search
                    search_results = tavily_client.search(
                        query=query,
                        search_depth="advanced",
                        max_results=self.config.max_search_results,
                        include_answer=True,
                        include_raw_content=True
                    )

                    # Process results
                    for result in search_results.get('results', []):
                        finding = ResearchFinding(
                            title=result.get('title', 'Unknown Title'),
                            url=result.get('url', ''),
                            content=self._extract_content(result),
                            relevance_score=result.get('score', 0.5),
                            source_type=self._classify_source(result.get('url', ''))
                        )
                        all_findings.append(finding)

                    # Small delay to be respectful to API
                    time.sleep(0.5)

                except Exception as e:
                    logger.warning(f"Search failed for query '{query}': {str(e)}")
                    continue

            # Sort by relevance and limit results
            all_findings.sort(key=lambda x: x.relevance_score, reverse=True)
            return all_findings[:self.config.max_search_results * 2]  # Keep top results

        except ImportError:
            console.print("[yellow]⚠️ Tavily not installed, using mock data[/yellow]")
            return self._get_mock_findings()
        except Exception as e:
            logger.error(f"Research execution failed: {str(e)}")
            return self._get_mock_findings()

    def _extract_content(self, result: Dict) -> str:
        """Extract and clean content from search result"""
        content = result.get('content', '') or result.get('raw_content', '')
        if len(content) > self.config.max_content_length:
            content = content[:self.config.max_content_length] + "..."
        return content

    def _classify_source(self, url: str) -> str:
        """Classify the type of source based on URL"""
        if not url:
            return "unknown"

        url_lower = url.lower()
        if 'stackoverflow.com' in url_lower:
            return "forum"
        elif 'github.com' in url_lower:
            return "code_repository"
        elif 'docs.' in url_lower or 'documentation' in url_lower:
            return "documentation"
        elif any(blog_indicator in url_lower for blog_indicator in ['blog', 'medium.com', 'dev.to']):
            return "blog"
        elif any(news_indicator in url_lower for news_indicator in ['news', 'techcrunch', 'reuters']):
            return "news"
        else:
            return "article"

    def _get_mock_findings(self) -> List[ResearchFinding]:
        """Fallback mock findings for demo purposes"""
        return [
            ResearchFinding(
                title="Official Documentation - Best Practices",
                url="https://example.com/docs",
                content="Comprehensive guide covering industry best practices and recommended approaches...",
                relevance_score=0.95,
                source_type="documentation"
            ),
            ResearchFinding(
                title="Stack Overflow Discussion",
                url="https://stackoverflow.com/questions/example",
                content="Community discussion with practical examples and solutions from experienced developers...",
                relevance_score=0.87,
                source_type="forum"
            ),
            ResearchFinding(
                title="Technical Blog Post",
                url="https://techblog.com/article",
                content="In-depth analysis with real-world case studies and implementation details...",
                relevance_score=0.82,
                source_type="blog"
            )
        ]
    
    async def synthesize_intelligent_answer(self, question: str, findings: List[ResearchFinding], plan: ResearchPlan) -> str:
        """
        Use OpenAI to synthesize a comprehensive answer from research findings
        """
        try:
            # Prepare context from findings
            context_parts = []
            for i, finding in enumerate(findings[:8], 1):  # Limit to top 8 findings
                context_parts.append(f"""
Source {i}: {finding.title} ({finding.source_type})
URL: {finding.url}
Relevance: {finding.relevance_score:.2f}
Content: {finding.content[:1000]}...
""")

            context = "\n".join(context_parts)

            synthesis_prompt = f"""
You are an expert researcher tasked with synthesizing information from multiple sources to answer a question comprehensively.

QUESTION: {question}

RESEARCH STRATEGY USED: {plan.strategy}

RESEARCH FINDINGS:
{context}

Please provide a comprehensive, well-structured answer that:
1. Directly answers the question
2. Synthesizes information from multiple sources
3. Provides specific examples and details where relevant
4. Includes proper citations with source numbers
5. Highlights any conflicting information or limitations
6. Uses clear, professional language

Structure your response with:
- A clear, direct answer to the question
- Supporting details and examples
- Key takeaways or recommendations
- Proper source citations [Source X]

Make the answer informative, actionable, and well-organized.
"""

            response = self.openai_client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": synthesis_prompt}],
                temperature=self.config.temperature,
                max_tokens=2000
            )

            answer = response.choices[0].message.content

            # Add source list at the end
            source_list = "\n\n## Sources:\n"
            for i, finding in enumerate(findings[:8], 1):
                source_list += f"{i}. [{finding.title}]({finding.url}) - {finding.source_type.title()}\n"

            return answer + source_list

        except Exception as e:
            logger.error(f"Answer synthesis failed: {str(e)}")
            return self._create_fallback_answer(question, findings)

    def _create_fallback_answer(self, question: str, findings: List[ResearchFinding]) -> str:
        """Fallback answer synthesis if AI synthesis fails"""
        answer = f"Based on my research across {len(findings)} sources:\n\n"
        answer += f"**Question:** {question}\n\n"

        if findings:
            answer += "**Key Findings:**\n"
            for i, finding in enumerate(findings[:3], 1):
                answer += f"{i}. From {finding.source_type}: {finding.content[:200]}...\n\n"

            answer += "**Sources:**\n"
            for i, finding in enumerate(findings[:5], 1):
                answer += f"{i}. [{finding.title}]({finding.url})\n"
        else:
            answer += "I apologize, but I wasn't able to gather sufficient information to provide a comprehensive answer."

        return answer

    def _display_research_plan(self, plan: ResearchPlan):
        """Display the research plan in a nice format"""
        table = Table(title="🎯 Research Plan", border_style="blue")
        table.add_column("Aspect", style="bold")
        table.add_column("Details")

        table.add_row("Strategy", plan.strategy)
        table.add_row("Search Queries", "\n".join([f"• {q}" for q in plan.search_queries]))
        table.add_row("Expected Sources", ", ".join(plan.expected_sources))
        table.add_row("Focus Areas", ", ".join(plan.focus_areas))

        console.print(table)

    def _store_research_session(self, question: str, plan: ResearchPlan, findings: List[ResearchFinding], answer: str):
        """Store research session in memory"""
        session = {
            "timestamp": datetime.now(),
            "question": question,
            "plan": plan,
            "findings_count": len(findings),
            "answer_length": len(answer),
            "sources": [f.url for f in findings[:5]]
        }
        self.memory.append(session)

        # Keep only last 10 sessions
        if len(self.memory) > 10:
            self.memory = self.memory[-10:]

    def get_conversation_context(self) -> str:
        """Get recent conversation context for follow-up questions"""
        if not self.conversation_history:
            return ""

        recent_context = self.conversation_history[-4:]  # Last 2 exchanges
        context = "Recent conversation:\n"
        for entry in recent_context:
            role = "Human" if entry["role"] == "user" else "Assistant"
            content = entry["content"][:200] + "..." if len(entry["content"]) > 200 else entry["content"]
            context += f"{role}: {content}\n\n"

        return context

# Enhanced Demo Functions
async def demo_research_agent(config_file: Optional[str] = None):
    """Comprehensive demo showcasing the enhanced research agent"""
    console.print(Panel.fit(
        "[bold blue]🚀 Enhanced Research Agent Demo[/bold blue]\n"
        "Demonstrating intelligent research capabilities for engineering teams",
        border_style="blue"
    ))

    # Initialize agent with configuration
    try:
        agent = EnhancedResearchAgent(config_file=config_file)
    except ValueError as e:
        console.print(f"[red]❌ Configuration Error: {e}[/red]")
        console.print("[yellow]💡 Run 'python setup_config.py' to configure your API keys[/yellow]")
        return

    # Demo scenarios
    demo_questions = [
        "What are the latest best practices for API rate limiting in 2024?",
        "How to implement microservices authentication with JWT tokens?",
        "What are the current trends in machine learning model deployment?",
        "Explain the differences between GraphQL and REST APIs with examples"
    ]

    console.print("\n[bold green]Available Demo Questions:[/bold green]")
    for i, question in enumerate(demo_questions, 1):
        console.print(f"{i}. {question}")

    console.print("\n[bold yellow]Running Demo with Question 1...[/bold yellow]")

    try:
        result = await agent.research_and_answer(demo_questions[0])

        # Display result
        console.print("\n" + "="*80)
        console.print(Panel(
            Markdown(result),
            title="🎯 Research Result",
            border_style="green"
        ))

        # Show memory
        console.print(f"\n[dim]Memory sessions stored: {len(agent.memory)}[/dim]")

    except Exception as e:
        console.print(f"[red]Demo failed: {str(e)}[/red]")
        console.print("[yellow]Make sure to set up your API keys in .env file[/yellow]")

def interactive_demo(config_file: Optional[str] = None):
    """Interactive demo for live presentations"""
    import asyncio

    console.print(Panel.fit(
        "[bold magenta]🎪 Interactive Research Agent Demo[/bold magenta]\n"
        "Ask any question and watch the agent research and respond!",
        border_style="magenta"
    ))

    try:
        agent = EnhancedResearchAgent(config_file=config_file)
    except ValueError as e:
        console.print(f"[red]❌ Configuration Error: {e}[/red]")
        console.print("[yellow]💡 Run 'python setup_config.py' to configure your API keys[/yellow]")
        return

    while True:
        try:
            question = input("\n🤔 Enter your research question (or 'quit' to exit): ").strip()

            if question.lower() in ['quit', 'exit', 'q']:
                console.print("[bold blue]👋 Thanks for trying the Enhanced Research Agent![/bold blue]")
                break

            if not question:
                continue

            # Run research
            result = asyncio.run(agent.research_and_answer(question))

            # Display result
            console.print("\n" + "="*80)
            console.print(Panel(
                Markdown(result),
                title="🎯 Research Result",
                border_style="green"
            ))

        except KeyboardInterrupt:
            console.print("\n[bold blue]👋 Demo interrupted. Goodbye![/bold blue]")
            break
        except Exception as e:
            console.print(f"[red]Error: {str(e)}[/red]")

if __name__ == "__main__":
    import asyncio
    import sys

    # Check for config file argument
    config_file = None
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
        console.print(f"[blue]Using config file: {config_file}[/blue]")

    console.print("[bold cyan]Choose demo mode:[/bold cyan]")
    console.print("1. Automated demo")
    console.print("2. Interactive demo")
    console.print("3. Setup configuration")

    choice = input("Enter choice (1, 2, or 3): ").strip()

    if choice == "3":
        # Run configuration setup
        try:
            import subprocess
            subprocess.run([sys.executable, "setup_config.py"])
        except Exception as e:
            console.print(f"[red]❌ Failed to run setup: {e}[/red]")
            console.print("[yellow]💡 Try running: python setup_config.py[/yellow]")
    elif choice == "2":
        interactive_demo(config_file)
    else:
        asyncio.run(demo_research_agent(config_file))