#!/usr/bin/env python3
"""
Configuration setup script for Enhanced Research Agent
Helps users set up their API keys and preferences
"""

import json
import os
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()

def setup_api_keys():
    """Interactive setup for API keys"""
    console.print(Panel.fit(
        "[bold blue]🔑 API Keys Setup[/bold blue]\n"
        "Let's configure your API keys for the Enhanced Research Agent",
        border_style="blue"
    ))
    
    config = {}
    
    # OpenAI API Key (Required)
    console.print("\n[bold green]OpenAI API Key (Required)[/bold green]")
    console.print("Get your API key from: https://platform.openai.com/api-keys")
    
    openai_key = Prompt.ask(
        "Enter your OpenAI API key",
        password=True,
        show_default=False
    )
    
    if not openai_key or openai_key.strip() == "":
        console.print("[red]❌ OpenAI API key is required![/red]")
        return None
    
    config["openai_api_key"] = openai_key.strip()
    
    # Tavily API Key (Optional)
    console.print("\n[bold yellow]Tavily API Key (Optional but Recommended)[/bold yellow]")
    console.print("Get your API key from: https://tavily.com")
    console.print("Without this, the agent will use mock search data")
    
    use_tavily = Confirm.ask("Do you want to configure Tavily for real web search?")
    
    if use_tavily:
        tavily_key = Prompt.ask(
            "Enter your Tavily API key",
            password=True,
            show_default=False,
            default=""
        )
        config["tavily_api_key"] = tavily_key.strip() if tavily_key else None
    else:
        config["tavily_api_key"] = None
    
    return config

def setup_preferences():
    """Setup agent preferences"""
    console.print("\n[bold cyan]🎛️ Agent Preferences[/bold cyan]")
    
    preferences = {}
    
    # Model selection
    models = ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]
    console.print("\nAvailable models:")
    for i, model in enumerate(models, 1):
        console.print(f"  {i}. {model}")
    
    model_choice = Prompt.ask(
        "Choose model",
        choices=["1", "2", "3"],
        default="1"
    )
    preferences["model_name"] = models[int(model_choice) - 1]
    
    # Search results
    preferences["max_search_results"] = int(Prompt.ask(
        "Maximum search results per query",
        default="5"
    ))
    
    # Research timeout
    preferences["research_timeout"] = int(Prompt.ask(
        "Research timeout (seconds)",
        default="30"
    ))
    
    # Debug mode
    preferences["debug_mode"] = Confirm.ask(
        "Enable debug mode?",
        default=False
    )
    
    return preferences

def create_config_file(api_keys, preferences):
    """Create the configuration file"""
    config = {
        # API Keys
        **api_keys,
        "serp_api_key": None,
        "google_cse_id": None,
        "google_api_key": None,
        
        # Preferences
        **preferences,
        "max_content_length": 8000,
        "temperature": 0.3,
        "log_level": "DEBUG" if preferences.get("debug_mode", False) else "INFO"
    }
    
    config_file = Path("agent_config.json")
    
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        console.print(f"\n[green]✅ Configuration saved to {config_file}[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Failed to save configuration: {e}[/red]")
        return False

def display_config_summary(config_file="agent_config.json"):
    """Display current configuration"""
    if not Path(config_file).exists():
        console.print("[red]❌ Configuration file not found[/red]")
        return
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        table = Table(title="🔧 Current Configuration", border_style="green")
        table.add_column("Setting", style="bold")
        table.add_column("Value")
        
        # API Keys
        table.add_row("OpenAI API Key", "✅ Configured" if config.get("openai_api_key") else "❌ Missing")
        table.add_row("Tavily API Key", "✅ Configured" if config.get("tavily_api_key") else "⚠️ Not set")
        
        # Settings
        table.add_row("Model", config.get("model_name", "gpt-4"))
        table.add_row("Max Search Results", str(config.get("max_search_results", 5)))
        table.add_row("Research Timeout", f"{config.get('research_timeout', 30)}s")
        table.add_row("Debug Mode", "✅ Enabled" if config.get("debug_mode", False) else "❌ Disabled")
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]❌ Failed to read configuration: {e}[/red]")

def main():
    """Main setup function"""
    console.print(Panel.fit(
        "[bold magenta]🤖 Enhanced Research Agent - Configuration Setup[/bold magenta]\n"
        "Let's set up your API keys and preferences!",
        border_style="magenta"
    ))
    
    # Check if config already exists
    config_file = Path("agent_config.json")
    if config_file.exists():
        console.print("\n[yellow]⚠️ Configuration file already exists[/yellow]")
        display_config_summary()
        
        if not Confirm.ask("\nDo you want to reconfigure?"):
            console.print("[blue]👍 Using existing configuration[/blue]")
            return
    
    # Setup API keys
    api_keys = setup_api_keys()
    if not api_keys:
        console.print("[red]❌ Setup cancelled[/red]")
        return
    
    # Setup preferences
    preferences = setup_preferences()
    
    # Create config file
    if create_config_file(api_keys, preferences):
        console.print("\n" + "="*50)
        console.print("[bold green]🎉 Configuration Complete![/bold green]")
        display_config_summary()
        
        console.print("\n[bold blue]Next Steps:[/bold blue]")
        console.print("1. Run: python Agent_WebSearch.py")
        console.print("2. Or run: python setup_demo.py")
        console.print("3. Check README.md for more information")
    else:
        console.print("[red]❌ Configuration setup failed[/red]")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        console.print("\n[yellow]👋 Setup cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Setup failed: {e}[/red]")
